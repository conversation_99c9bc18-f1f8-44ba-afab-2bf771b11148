/**
 * 五维性格测评 2.0 表单逻辑组合式函数
 */

import { ref, computed, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import type { ConfigGroup, WeightTableRow, NormTableRow, DimensionItem, TableColumn, ComponentFormData, ApiConfigItem } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';
import { useCharacter2Store } from '../store';

export function useCharacter2Form(tableType: 'key-potential' | 'job-quality' | 'team-role' = 'key-potential') {
    const route = useRoute();
    const store = useCharacter2Store();

    // 内部状态
    const configGroups = ref<ConfigGroup[]>([{ id: '1', name: '配置组1' }]);
    const weightTableData = ref<WeightTableRow[]>([]);
    const normTableData = ref<NormTableRow[]>([]);

    // 表格列配置
    const weightColumns = ref<TableColumn[]>([]);
    const normColumns = ref<TableColumn[]>([]);

    // 权重验证错误
    const weightSumError = ref(false);

    // 计算属性
    const productId = computed(() => route.query?.productId || CHARACTER2_CONSTANTS.PRODUCT_ID);

    const canAddConfigGroup = computed(() => {
        // 团队角色类型不允许添加配置组
        if (tableType === 'team-role') return false;
        return configGroups.value.length < CHARACTER2_CONSTANTS.MAX_CONFIG_GROUPS;
    });

    const canRemoveConfigGroup = computed(() => {
        // 团队角色类型不允许删除配置组
        if (tableType === 'team-role') return false;
        return configGroups.value.length > 1;
    });

    /**
     * 初始化表格数据
     */
    function initTableData() {
        // 根据表格类型初始化权重表数据
        if (tableType === 'team-role') {
            // 团队角色使用固定的9个角色
            weightTableData.value = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((roleName, index) => {
                const rowData: WeightTableRow = {
                    dimensionName: roleName,
                    encryptDimensionId: (index + 1).toString(),
                };

                // 为每个配置组添加权重字段
                configGroups.value.forEach((group) => {
                    rowData[`weight_${group.id}`] = 0;
                });

                return rowData;
            });
        } else {
            // 其他类型使用维度数据
            weightTableData.value = store.dimensionList.map((dimension) => {
                const rowData: WeightTableRow = {
                    dimensionName: dimension.name,
                    encryptDimensionId: dimension.encryptId,
                };

                // 为每个配置组添加权重字段
                configGroups.value.forEach((group) => {
                    rowData[`weight_${group.id}`] = 0;
                });

                return rowData;
            });
        }

        // 初始化常模表数据
        normTableData.value = configGroups.value.map((group) => ({
            groupId: group.id,
            groupName: group.name,
            avgScore: 0,
            stdDev: 0,
        }));

        updateColumns(tableType);
    }

    /**
     * 更新列配置
     */
    function updateColumns(tableType: 'key-potential' | 'job-quality' | 'team-role' = 'key-potential') {
        // 权重表第一列统一使用 "二级维度" 标签
        weightColumns.value = [
            {
                label: '二级维度',
                field: 'dimension',
                width: 200,
            },
            ...configGroups.value.map((group) => ({
                label: group.name,
                field: `weight_${group.id}`,
                width: 160,
            })),
        ];

        // 更新常模表列配置 - 根据表格类型设置不同的标签
        let normFirstColumnLabel = '关键潜在素质';
        if (tableType === 'job-quality') {
            normFirstColumnLabel = '岗位素质模型';
        } else if (tableType === 'team-role') {
            normFirstColumnLabel = '团队角色';
        }

        normColumns.value = [
            {
                label: normFirstColumnLabel,
                field: 'groupName',
                width: 200,
            },
            {
                label: '常模平均分',
                field: 'avgScore',
                width: 150,
            },
            {
                label: '常模标准差',
                field: 'stdDev',
                width: 150,
            },
        ];
    }

    /**
     * 添加配置组
     */
    function addConfigGroup() {
        if (!canAddConfigGroup.value) return;

        const newId = Date.now().toString();
        const newGroup: ConfigGroup = {
            id: newId,
            name: `配置组${configGroups.value.length + 1}`,
        };

        configGroups.value.push(newGroup);

        // 为现有权重表数据添加新配置组的字段
        weightTableData.value.forEach((row) => {
            row[`weight_${newId}`] = 0;
        });

        // 为常模表添加新的配置组行
        normTableData.value.push({
            groupId: newId,
            groupName: newGroup.name,
            avgScore: 0,
            stdDev: 0,
        });

        updateColumns(tableType);
    }

    /**
     * 删除配置组
     */
    function removeConfigGroup(groupId: string) {
        if (!canRemoveConfigGroup.value) return;

        const index = configGroups.value.findIndex((group) => group.id === groupId);
        if (index > -1) {
            configGroups.value.splice(index, 1);

            // 从权重表数据中移除对应字段
            weightTableData.value.forEach((row) => {
                delete row[`weight_${groupId}`];
            });

            // 从常模表中移除对应的行
            const normIndex = normTableData.value.findIndex((row) => row.groupId === groupId);
            if (normIndex > -1) {
                normTableData.value.splice(normIndex, 1);
            }

            updateColumns(tableType);
        }
    }

    /**
     * 验证权重和
     */
    function validateWeightSum() {
        store.validateWeights(configGroups.value, weightTableData.value);
        weightSumError.value = store.weightValidationState.hasError;
    }

    /**
     * 从API数据初始化内部状态
     */
    function initFromApiData(apiData: ApiConfigItem[]) {
        try {
            if (!Array.isArray(apiData) || (apiData.length === 0 && tableType !== 'team-role')) {
                return;
            }

            // 重建配置组
            const groups: ConfigGroup[] = [];
            const normData: NormTableRow[] = [];

            // 处理每个配置组
            if (apiData.length > 0) {
                apiData.forEach((item, index) => {
                    if (item.headName) {
                        const groupId = item.encId || (index + 1).toString();

                        // 添加配置组
                        groups.push({
                            id: groupId,
                            name: item.headName,
                        });

                        // 处理常模数据
                        normData.push({
                            groupId: groupId,
                            groupName: item.headName,
                            avgScore: item.normalAverageScore || 0,
                            stdDev: item.normalStandardDeviation || 0,
                        });
                    }
                });
            }

            // 重建权重表数据
            const weightData: WeightTableRow[] = [];

            if (tableType === 'team-role') {
                // 团队角色类型：确保始终有9个固定的团队角色
                CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.forEach((roleName, index) => {
                    const weightRow: WeightTableRow = {
                        dimensionName: roleName,
                        encryptDimensionId: (index + 1).toString(),
                    };

                    // 为每个配置组添加权重字段
                    if (apiData.length > 0) {
                        apiData.forEach((item, itemIndex) => {
                            const groupId = item.encId || (itemIndex + 1).toString();
                            // 尝试从API数据中找到对应的权重值
                            const rowData = item.rowDataList?.find((r) => r.dimensionName === roleName || r.encDimensionId === (index + 1).toString());
                            weightRow[`weight_${groupId}`] = rowData?.weight || 0;
                        });
                    } else {
                        // 如果没有API数据，使用默认配置组
                        weightRow[`weight_1`] = 0;
                    }

                    weightData.push(weightRow);
                });
            } else {
                // 其他类型：使用API返回的数据
                if (apiData[0]?.rowDataList && Array.isArray(apiData[0].rowDataList)) {
                    apiData[0].rowDataList.forEach((row) => {
                        const weightRow: WeightTableRow = {
                            dimensionName: row.dimensionName,
                            encryptDimensionId: row.encDimensionId,
                        };

                        // 为每个配置组添加权重字段
                        apiData.forEach((item, itemIndex) => {
                            const groupId = item.encId || (itemIndex + 1).toString();
                            const rowData = item.rowDataList?.find((r) => r.encDimensionId === row.encDimensionId);
                            weightRow[`weight_${groupId}`] = rowData?.weight || 0;
                        });

                        weightData.push(weightRow);
                    });
                }
            }

            // 更新内部状态
            if (groups.length > 0 || (tableType === 'team-role' && weightData.length > 0)) {
                // 对于团队角色类型，如果没有配置组数据，使用默认配置组
                if (tableType === 'team-role' && groups.length === 0) {
                    configGroups.value = [{ id: '1', name: '配置组1' }];
                    normTableData.value = [{ groupId: '1', groupName: '配置组1', avgScore: 0, stdDev: 0 }];
                } else {
                    configGroups.value = groups;
                    normTableData.value = normData;
                }

                weightTableData.value = weightData;
                updateColumns(tableType);

                // 强制触发响应式更新
                nextTick(() => {
                    validateWeightSum();
                });
            }
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('从API数据初始化失败:', error);
        }
    }

    /**
     * 获取当前表单数据
     */
    function getCurrentFormData(): ComponentFormData {
        return {
            configGroups: configGroups.value,
            weightTableData: weightTableData.value,
            normTableData: normTableData.value,
            dimensionList: store.dimensionList,
        };
    }

    /**
     * 重置表单数据
     */
    function resetFormData() {
        configGroups.value = [{ id: '1', name: '配置组1' }];
        weightTableData.value = [];
        normTableData.value = [];
        weightSumError.value = false;
        initTableData();
    }

    // 监听配置组名称变化，同步到常模表
    watch(
        () => configGroups.value.map((group) => ({ id: group.id, name: group.name })),
        (newGroups) => {
            newGroups.forEach((group) => {
                const normItem = normTableData.value.find((item) => item.groupId === group.id);
                if (normItem) {
                    normItem.groupName = group.name;
                }
            });
        },
        { deep: true }
    );

    // 监听维度数据变化，重新初始化表格（团队角色类型不依赖维度数据）
    watch(
        () => store.dimensionList,
        (newDimensionList) => {
            if (tableType !== 'team-role' && newDimensionList.length > 0) {
                initTableData();
            }
        },
        { immediate: tableType !== 'team-role' }
    );

    // 对于团队角色类型，立即初始化表格数据
    if (tableType === 'team-role') {
        initTableData();
    }

    return {
        // 状态
        configGroups,
        weightTableData,
        normTableData,
        weightColumns,
        normColumns,
        weightSumError,

        // 计算属性
        productId,
        canAddConfigGroup,
        canRemoveConfigGroup,

        // 方法
        initTableData,
        updateColumns,
        addConfigGroup,
        removeConfigGroup,
        validateWeightSum,
        initFromApiData,
        getCurrentFormData,
        resetFormData,
    };
}
